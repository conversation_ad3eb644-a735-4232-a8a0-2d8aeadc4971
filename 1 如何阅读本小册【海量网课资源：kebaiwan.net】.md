# 1 如何阅读本小册
在正式开始小册之前，我先来简单介绍下小册的内容，以及大家该怎样阅读小册，才能更好地吸收其中的知识点。

**首先，本小册是围绕 TypeScript 类型体操来讲的，这是 TypeScript 中最难的部分。**

为了帮助大家理解和掌握，我总结出了类型编程六大套路，也就是小册的 5 到 10 节，这是小册中最重点的部分，大家也要重点关注。同时，我也为每个套路提供了大量的实战案例，以及 playground 的链接，建议点进去亲自试一下。

![image](images/ReTL1r2r8rn-wMEU1V9xQMBQ8VJ5eHk32HdbxciGBgE.webp)

当然，每个案例的集合我也都总结在了文章的最后。

![image](images/l97Led76SSIaBGpU0MBLM1BkX7K6MBEp-_iHEGuCzno.webp)

六个套路介绍完之后，为了方便记忆，我总结了类型体操顺口溜，并解释了每句的含义，时不时读两遍，配合实战案例，吸收率更好。

说完了最重点的部分，我们再回过头来说说整个小册的构建思路。

在介绍这些套路之前，第 2、3、4 节主要会讲解一些理论知识，包括类型、类型安全、静态类型、动态类型、三种类型系统等等。其中，第 4 节主要讲解 TypeScript 类型系统中的类型和支持的类型运算，这些过一遍就行，在后面的实战中会大量用到，自然而然就记住了。**理论还是很重要的，它提升的是我们的认知，也就是看待技术的视角。**

类型体操顺口溜之后，我们会实现内置的高级类型。其实学完类型体操的套路之后，你会发现内置的类型实现起来太简单了。然后我单独准备了一节实际案例来说明类型体操的意义，以及它在项目中有发挥了什么作用。后面是大量的综合实战，难度会比单独讲每个套路的时候大一些。综合运用各种套路，就能实现各种类型编程逻辑了。

接着，我会讲解 TypeScript 类型检查的实现原理。在这个过程中，我们要自己实现类型检查。只有自己能实现了，才能真正理解 TypeScript 类型检查都做了啥。当然，我也会讲解阅读 TypeScript 源码的方法，带大家探究联合类型的分散特性的实现原理。不过，阅读 TypeScript 源码的必要性不大，它只是一个工具。因此，这一节只是扩展下技术视野。

最后就是小册的总结了，这一节我们会对小册整体的知识点都做个总结。

《Babel 插件通关秘籍》是我写的第一本小册，当时是第一次写，虽然内容很丰富，但表达上还有些不成熟。在写《TypeScript 类型体操通关秘籍》这本小册的时候，我已经有了一些写作经验，知道了该怎么写，所以会写得更清晰些。让小册读起来更清晰、易懂，阅读爽感更强，这也是我一直追求的。

当然，小册的内容难免还会有瑕疵。因此，内容和表达方面有任何问题，大家都可以在评论区、小册交流群和“神光的编程秘籍”公众号找到我，我会积极解答。

希望这本小册能让大家真正“通关”类型体操，成为类型编程高手，让我们一起加油吧！