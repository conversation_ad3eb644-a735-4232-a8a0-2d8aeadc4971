#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理文件名和图片路径中的"【海量网课资源：kebaiwan.net】"后缀
"""

import os
import re
import shutil
from pathlib import Path

def clean_filename(filename):
    """
    清理文件名，删除"【海量网课资源：kebaiwan.net】"后缀
    
    Args:
        filename (str): 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    # 删除"【海量网课资源：kebaiwan.net】"
    cleaned = filename.replace("【海量网课资源：kebaiwan.net】", "")
    return cleaned

def rename_files_in_directory(directory_path):
    """
    重命名目录中所有包含指定后缀的文件
    
    Args:
        directory_path (str): 目录路径
        
    Returns:
        dict: 重命名映射表 {old_name: new_name}
    """
    rename_map = {}
    directory = Path(directory_path)
    
    # 获取所有需要重命名的文件
    files_to_rename = []
    for file_path in directory.rglob("*"):
        if file_path.is_file() and "【海量网课资源：kebaiwan.net】" in file_path.name:
            files_to_rename.append(file_path)
    
    # 重命名文件
    for file_path in files_to_rename:
        old_name = file_path.name
        new_name = clean_filename(old_name)
        new_path = file_path.parent / new_name
        
        try:
            file_path.rename(new_path)
            rename_map[old_name] = new_name
            print(f"重命名: {old_name} -> {new_name}")
        except Exception as e:
            print(f"重命名失败 {old_name}: {e}")
    
    return rename_map

def update_markdown_references(directory_path, rename_map):
    """
    更新markdown文件中的图片引用路径
    
    Args:
        directory_path (str): 目录路径
        rename_map (dict): 文件重命名映射表
    """
    directory = Path(directory_path)
    
    # 查找所有markdown文件
    md_files = list(directory.glob("*.md"))
    
    for md_file in md_files:
        try:
            # 读取文件内容
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 更新图片引用路径
            for old_name, new_name in rename_map.items():
                # 匹配图片引用格式: ![image](images/filename)
                old_pattern = f"images/{re.escape(old_name)}"
                new_pattern = f"images/{new_name}"
                content = content.replace(old_pattern, new_pattern)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(md_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"更新markdown文件: {md_file.name}")
        
        except Exception as e:
            print(f"处理markdown文件失败 {md_file}: {e}")

def main():
    """
    主函数：执行清理操作
    """
    current_dir = "."
    
    print("开始清理文件名和图片路径...")
    print("=" * 50)
    
    # 1. 重命名所有文件
    print("1. 重命名文件...")
    rename_map = rename_files_in_directory(current_dir)
    
    print(f"\n总共重命名了 {len(rename_map)} 个文件")
    print("=" * 50)
    
    # 2. 更新markdown文件中的图片引用
    print("2. 更新markdown文件中的图片引用...")
    update_markdown_references(current_dir, rename_map)
    
    print("=" * 50)
    print("清理完成！")
    
    # 显示统计信息
    if rename_map:
        print(f"\n重命名文件统计:")
        for old_name, new_name in list(rename_map.items())[:5]:  # 只显示前5个
            print(f"  {old_name} -> {new_name}")
        if len(rename_map) > 5:
            print(f"  ... 还有 {len(rename_map) - 5} 个文件")

if __name__ == "__main__":
    main()
